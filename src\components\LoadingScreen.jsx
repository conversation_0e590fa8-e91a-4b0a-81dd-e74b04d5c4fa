import { useEffect, useState } from 'react'
import './LoadingScreen.css'

const LoadingScreen = ({ onLoadingComplete }) => {
    const [progress, setProgress] = useState(0)
    const [isVisible, setIsVisible] = useState(true)

    useEffect(() => {
        const interval = setInterval(() => {
            setProgress(prev => {
                if (prev >= 100) {
                    clearInterval(interval)
                    setTimeout(() => {
                        setIsVisible(false)
                        onLoadingComplete()
                    }, 500)
                    return 100
                }
                return prev + Math.random() * 15
            })
        }, 100)

        return () => clearInterval(interval)
    }, [onLoadingComplete])

    if (!isVisible) return null

    return (
        <div className={`loading-screen ${progress >= 100 ? 'fade-out' : ''}`}>
            <div className="loading-content">
                <div className="logo-container">
                    <div className="animated-logo">
                        <div className="logo-ring ring-1"></div>
                        <div className="logo-ring ring-2"></div>
                        <div className="logo-ring ring-3"></div>
                        <div className="logo-center">
                            <span>QH</span>
                        </div>
                    </div>
                </div>
                
                <div className="loading-text">
                    <h2>Qaswar Hussain</h2>
                    <p>MERN Stack Developer</p>
                </div>
                
                <div className="progress-container">
                    <div className="progress-bar">
                        <div 
                            className="progress-fill"
                            style={{ width: `${Math.min(progress, 100)}%` }}
                        ></div>
                    </div>
                    <span className="progress-text">{Math.floor(Math.min(progress, 100))}%</span>
                </div>
            </div>
            
            <div className="loading-particles">
                {[...Array(20)].map((_, i) => (
                    <div key={i} className={`particle particle-${i + 1}`}></div>
                ))}
            </div>
        </div>
    )
}

export default LoadingScreen
