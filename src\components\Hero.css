/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 100vh;
}

.hero-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.hero-content {
    text-align: left;
}

.hero-right {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    min-height: 500px;
    width: 100%;
    position: relative;
}

.hero-title {
    font-family: 'Poppins', sans-serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #b8bcc8;
    margin-bottom: 2rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: #8892b0;
    margin-bottom: 3rem;
    line-height: 1.8;
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.hero-scroll {
    margin-top: 2rem;
}

.scroll-indicator {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    color: #8892b0;
    font-size: 0.9rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    opacity: 0.1;
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #764ba2, #667eea);
    border-radius: 50%;
    top: 60%;
    right: 10%;
    animation-delay: -5s;
}

.shape-3 {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #667eea, #4facfe);
    border-radius: 50%;
    bottom: 30%;
    left: 20%;
    animation-delay: -10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #4facfe, #667eea);
    border-radius: 50%;
    top: 40%;
    right: 30%;
    animation-delay: -15s;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
    100% {
        transform: translateY(0px) rotate(360deg);
    }
}

.circular-3d-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
    z-index: 1;
}

.circular-3d-container canvas {
    width: 100% !important;
    height: 100% !important;
    min-height: 500px;
    max-height: 100vh;
    opacity: 0.9;
    border-radius: 0;
    display: block;
    background: transparent;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-content {
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        max-width: 100%;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .scroll-indicator {
        align-items: center;
    }
    
    .hero-container {
        padding: 0 15px;
    }
    
    .circular-3d-container {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
    }
}
