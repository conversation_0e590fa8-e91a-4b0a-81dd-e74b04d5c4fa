/* Enhanced Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: var(--bg-primary);
    overflow: hidden;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(79, 172, 254, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 40% 40%, rgba(79, 172, 254, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    z-index: 1;
    animation: gradientShift 20s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.hero-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-20);
    align-items: center;
    min-height: calc(100vh - 80px);
}

.hero-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.hero-content {
    text-align: left;
    position: relative;
}

.hero-content::before {
    content: '';
    position: absolute;
    top: -2rem;
    left: -2rem;
    width: 100px;
    height: 100px;
    background: var(--bg-secondary);
    border-radius: 50%;
    opacity: 0.1;
    filter: blur(40px);
    animation: float 6s ease-in-out infinite;
}

.hero-right {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 600px;
    width: 100%;
    position: relative;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: var(--text-7xl);
    font-weight: 900;
    margin-bottom: var(--space-4);
    line-height: var(--leading-none);
    background: var(--text-gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.03em;
    position: relative;
    text-shadow: 0 0 40px rgba(79, 172, 254, 0.3);
}

.hero-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60%;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-glow);
    animation: slideIn 1s ease-out 0.5s both;
}

@keyframes slideIn {
    from { width: 0; opacity: 0; }
    to { width: 60%; opacity: 1; }
}

.hero-subtitle {
    font-size: var(--text-2xl);
    color: var(--neutral-200);
    margin-bottom: var(--space-8);
    font-weight: 600;
    background: var(--text-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.01em;
}

.hero-description {
    font-size: var(--text-xl);
    color: var(--neutral-300);
    margin-bottom: var(--space-10);
    line-height: var(--leading-relaxed);
    max-width: 600px;
    font-weight: 400;
}

.hero-buttons {
    display: flex;
    gap: var(--space-6);
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-bottom: var(--space-12);
    position: relative;
}

.hero-buttons .btn {
    padding: var(--space-4) var(--space-10);
    font-size: var(--text-lg);
    font-weight: 600;
    min-width: 180px;
}

.hero-scroll {
    margin-top: var(--space-8);
    position: relative;
}

.scroll-indicator {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
    color: var(--neutral-400);
    font-size: var(--text-sm);
    font-weight: 500;
    animation: bounce 3s ease-in-out infinite;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.scroll-indicator:hover {
    color: var(--primary-blue);
    transform: translateY(-2px);
}

.scroll-indicator i {
    font-size: var(--text-lg);
    animation: arrowBounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}

@keyframes arrowBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(5px); }
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    opacity: 0.15;
    animation: float 20s infinite linear;
    backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.shape-1 {
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, #4facfe, #667eea);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    top: 15%;
    left: 8%;
    animation: float 25s infinite linear, morph 8s infinite ease-in-out;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #667eea, #4facfe);
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    top: 65%;
    right: 12%;
    animation: float 30s infinite linear, morph 10s infinite ease-in-out;
    animation-delay: -5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
    bottom: 25%;
    left: 15%;
    animation: float 22s infinite linear, morph 12s infinite ease-in-out;
    animation-delay: -10s;
}

.shape-4 {
    width: 140px;
    height: 140px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 20% 80% 80% 20% / 20% 20% 80% 80%;
    top: 35%;
    right: 25%;
    animation: float 35s infinite linear, morph 15s infinite ease-in-out;
    animation-delay: -15s;
}

.shape-5 {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #00f2fe, #4facfe);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    top: 80%;
    left: 50%;
    animation: float 18s infinite linear, morph 6s infinite ease-in-out;
    animation-delay: -20s;
}

.shape-6 {
    width: 90px;
    height: 90px;
    background: linear-gradient(45deg, #764ba2, #667eea);
    border-radius: 70% 30% 30% 70% / 60% 40% 60% 40%;
    top: 10%;
    right: 5%;
    animation: float 28s infinite linear, morph 9s infinite ease-in-out;
    animation-delay: -25s;
}

@keyframes float {
    0% {
        transform: translateY(0px) translateX(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-30px) translateX(15px) rotate(90deg);
    }
    50% {
        transform: translateY(-20px) translateX(-10px) rotate(180deg);
    }
    75% {
        transform: translateY(-40px) translateX(20px) rotate(270deg);
    }
    100% {
        transform: translateY(0px) translateX(0px) rotate(360deg);
    }
}

@keyframes morph {
    0%, 100% {
        border-radius: 40% 60% 60% 40% / 60% 30% 70% 40%;
    }
    25% {
        border-radius: 60% 40% 30% 70% / 70% 50% 50% 30%;
    }
    50% {
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    75% {
        border-radius: 70% 30% 40% 60% / 40% 70% 30% 60%;
    }
}

.professional-3d-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
    z-index: 1;
    border-radius: var(--radius-3xl);
    overflow: hidden;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    transition: all var(--transition-slow);
}

.professional-3d-container:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow-lg);
    border-color: rgba(255, 255, 255, 0.2);
}

.professional-3d-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(79, 172, 254, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 70% 70%, rgba(102, 126, 234, 0.15) 0%, transparent 60%),
        linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: shimmer 8s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.professional-3d-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    z-index: 1;
}

.professional-3d-container canvas {
    width: 100% !important;
    height: 100% !important;
    min-height: 600px;
    max-height: 100vh;
    opacity: 1;
    border-radius: var(--radius-3xl);
    display: block;
    background: transparent;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-content {
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        max-width: 100%;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .scroll-indicator {
        align-items: center;
    }
    
    .hero-container {
        padding: 0 15px;
    }
    
    .professional-3d-container {
        height: 350px;
        border-radius: 15px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
    }
}
