/* Skills Section */
.skills {
    padding: 100px 0;
    background: #0a0a0a;
}

.skills-content {
    max-width: 1000px;
    margin: 0 auto;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.skill-category {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-5px);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.skill-category h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.skill-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.skill-item:hover {
    transform: translateY(-5px);
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.skill-item img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.skill-item:hover img {
    transform: scale(1.1);
}

.skill-item span {
    font-size: 0.9rem;
    font-weight: 500;
    color: #e4e6ea;
    text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .skills-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .skill-category {
        padding: 1.5rem;
    }
    
    .skill-category h3 {
        font-size: 1.3rem;
    }
    
    .skill-items {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 0.8rem;
    }
    
    .skill-item {
        padding: 0.8rem;
    }
    
    .skill-item img {
        width: 35px;
        height: 35px;
    }
    
    .skill-item span {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .skills {
        padding: 80px 0;
    }
    
    .skill-category {
        padding: 1rem;
    }
    
    .skill-items {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .skill-item {
        padding: 0.5rem;
    }
    
    .skill-item img {
        width: 30px;
        height: 30px;
    }
}
