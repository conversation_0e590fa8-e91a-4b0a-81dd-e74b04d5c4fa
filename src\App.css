/* Enhanced CSS Custom Properties */
:root {
    /* Primary Colors */
    --primary-blue: #4facfe;
    --primary-purple: #667eea;
    --primary-dark-blue: #3b82f6;
    --primary-light-blue: #60a5fa;

    /* Secondary Colors */
    --secondary-purple: #764ba2;
    --secondary-pink: #f093fb;
    --secondary-cyan: #00f2fe;
    --secondary-indigo: #6366f1;

    /* Neutral Colors */
    --neutral-900: #0a0a0a;
    --neutral-800: #1a1a2e;
    --neutral-700: #16213e;
    --neutral-600: #2d3748;
    --neutral-500: #4a5568;
    --neutral-400: #718096;
    --neutral-300: #8892b0;
    --neutral-200: #b8bcc8;
    --neutral-100: #e4e6ea;
    --neutral-50: #f7fafc;

    /* Background Gradients */
    --bg-primary: linear-gradient(135deg, var(--neutral-900) 0%, var(--neutral-800) 50%, var(--neutral-700) 100%);
    --bg-secondary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
    --bg-tertiary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--primary-purple) 100%);

    /* Text Gradients */
    --text-gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
    --text-gradient-secondary: linear-gradient(135deg, var(--neutral-100) 0%, var(--primary-blue) 50%, var(--primary-purple) 100%);
    --text-gradient-accent: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-purple) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(79, 172, 254, 0.3);
    --shadow-glow-lg: 0 0 40px rgba(79, 172, 254, 0.4);

    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-backdrop: blur(20px);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    --text-7xl: 4.5rem;
    --text-8xl: 6rem;
    --text-9xl: 8rem;

    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Custom Cursor */
* {
    cursor: none;
}

.custom-cursor {
    position: fixed;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #4facfe, #667eea);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: difference;
}

.custom-cursor-follower {
    position: fixed;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(79, 172, 254, 0.5);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: all 0.3s ease;
}

/* Smooth scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a2e;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #4facfe, #667eea);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #667eea, #4facfe);
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

body {
    font-family: var(--font-primary);
    line-height: var(--leading-relaxed);
    color: var(--neutral-100);
    background: var(--bg-primary);
    background-attachment: fixed;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* Enhanced Typography System */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: var(--leading-tight);
    letter-spacing: -0.025em;
    margin-bottom: var(--space-4);
}

h1 {
    font-size: var(--text-5xl);
    font-weight: 800;
    line-height: var(--leading-none);
}

h2 {
    font-size: var(--text-4xl);
    font-weight: 700;
}

h3 {
    font-size: var(--text-3xl);
    font-weight: 600;
}

h4 {
    font-size: var(--text-2xl);
    font-weight: 600;
}

h5 {
    font-size: var(--text-xl);
    font-weight: 500;
}

h6 {
    font-size: var(--text-lg);
    font-weight: 500;
}

p {
    margin-bottom: var(--space-4);
    color: var(--neutral-300);
    line-height: var(--leading-relaxed);
}

.text-gradient {
    background: var(--text-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-secondary {
    background: var(--text-gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-accent {
    background: var(--text-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Code and Monospace */
code, pre {
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--space-1) var(--space-2);
}

pre {
    padding: var(--space-4);
    overflow-x: auto;
    white-space: pre-wrap;
}

/* Enhanced Container System */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    position: relative;
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.container-2xl {
    max-width: 1536px;
}

/* Enhanced Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-8);
    text-decoration: none;
    border-radius: var(--radius-full);
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: var(--text-base);
    line-height: var(--leading-none);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Primary Button - Enhanced Glassmorphism */
.btn-primary {
    background: var(--bg-secondary);
    color: white;
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: var(--glass-backdrop);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
    z-index: 1;
}

.btn-primary::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    border-radius: inherit;
    z-index: 0;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow-lg);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-primary:active {
    transform: translateY(-1px) scale(0.98);
    transition: all var(--transition-fast);
}

.btn-primary span {
    position: relative;
    z-index: 2;
}

/* Secondary Button - Glass Border */
.btn-secondary {
    background: var(--glass-bg);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    backdrop-filter: var(--glass-backdrop);
    box-shadow: var(--shadow-md);
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--bg-secondary);
    transition: width var(--transition-normal);
    z-index: 0;
    border-radius: inherit;
}

.btn-secondary::after {
    content: '';
    position: absolute;
    inset: 1px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    border-radius: calc(var(--radius-full) - 1px);
    z-index: 1;
}

.btn-secondary:hover::before {
    width: 100%;
}

.btn-secondary:hover {
    color: white;
    transform: translateY(-3px) scale(1.02);
    border-color: var(--primary-purple);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary:active {
    transform: translateY(-1px) scale(0.98);
    transition: all var(--transition-fast);
}

.btn-secondary span {
    position: relative;
    z-index: 2;
}

/* Ghost Button */
.btn-ghost {
    background: transparent;
    color: var(--neutral-300);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.btn-ghost:hover {
    background: var(--glass-bg);
    color: var(--neutral-100);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Outline Button */
.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-6);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--space-4) var(--space-10);
    font-size: var(--text-lg);
}

.btn-xl {
    padding: var(--space-5) var(--space-12);
    font-size: var(--text-xl);
}

/* Enhanced Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--space-20);
    position: relative;
}

.section-header::before {
    content: '';
    position: absolute;
    top: -2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-glow);
}

.section-title {
    font-family: var(--font-heading);
    font-size: var(--text-5xl);
    font-weight: 800;
    margin-bottom: var(--space-4);
    background: var(--text-gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    position: relative;
}

.section-subtitle {
    font-size: var(--text-xl);
    color: var(--neutral-300);
    max-width: 700px;
    margin: 0 auto;
    line-height: var(--leading-relaxed);
    font-weight: 400;
}

/* Enhanced Highlight Text */
.highlight {
    background: var(--text-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    opacity: 0.6;
}

/* Glass Card Component */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    border-color: rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.transition-all { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .btn {
        padding: 10px 25px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 1.8rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
}
