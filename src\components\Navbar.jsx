import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import './Navbar.css'

const Navbar = () => {
    const [isScrolled, setIsScrolled] = useState(false)
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
    const [activeSection, setActiveSection] = useState('home')

    useEffect(() => {
        const handleScroll = () => {
            const scrollTop = window.pageYOffset
            setIsScrolled(scrollTop > 50)

            // Update active section based on scroll position
            const sections = ['home', 'about', 'projects', 'skills', 'contact']
            const currentSection = sections.find(section => {
                const element = document.getElementById(section)
                if (element) {
                    const rect = element.getBoundingClientRect()
                    return rect.top <= 100 && rect.bottom >= 100
                }
                return false
            })

            if (currentSection) {
                setActiveSection(currentSection)
            }
        }

        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [])

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen)
    }

    const handleNavClick = (e, targetId) => {
        e.preventDefault()
        const targetElement = document.getElementById(targetId)
        if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' })
        }
        setIsMobileMenuOpen(false)
        setActiveSection(targetId)
    }

    const navItems = [
        { id: 'home', label: 'Home', icon: 'fas fa-home' },
        { id: 'about', label: 'About', icon: 'fas fa-user' },
        { id: 'projects', label: 'Projects', icon: 'fas fa-code' },
        { id: 'skills', label: 'Skills', icon: 'fas fa-cogs' },
        { id: 'contact', label: 'Contact', icon: 'fas fa-envelope' }
    ]

    return (
        <motion.nav
            className={`navbar ${isScrolled ? 'scrolled' : ''}`}
            id="navbar"
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
        >
            <div className="nav-container">
                <motion.div
                    className="nav-logo"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                >
                    <span className="logo-text">QH</span>
                    <div className="logo-dot"></div>
                </motion.div>

                <ul className={`nav-menu ${isMobileMenuOpen ? 'active' : ''}`} id="nav-menu">
                    {navItems.map((item, index) => (
                        <motion.li
                            key={item.id}
                            className="nav-item"
                            initial={{ opacity: 0, y: -20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1, duration: 0.3 }}
                        >
                            <a
                                href={`#${item.id}`}
                                className={`nav-link ${activeSection === item.id ? 'active' : ''}`}
                                onClick={(e) => handleNavClick(e, item.id)}
                            >
                                <i className={item.icon}></i>
                                <span>{item.label}</span>
                                {activeSection === item.id && (
                                    <motion.div
                                        className="nav-indicator"
                                        layoutId="nav-indicator"
                                        transition={{ type: "spring", stiffness: 300, damping: 30 }}
                                    />
                                )}
                            </a>
                        </motion.li>
                    ))}
                </ul>

                <motion.div
                    className={`nav-toggle ${isMobileMenuOpen ? 'active' : ''}`}
                    id="mobile-menu"
                    onClick={toggleMobileMenu}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                >
                    <span className="bar"></span>
                    <span className="bar"></span>
                    <span className="bar"></span>
                </motion.div>
            </div>

            {/* Mobile Menu Overlay */}
            <AnimatePresence>
                {isMobileMenuOpen && (
                    <motion.div
                        className="mobile-menu-overlay"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        onClick={() => setIsMobileMenuOpen(false)}
                    />
                )}
            </AnimatePresence>
        </motion.nav>
    )
}

export default Navbar
