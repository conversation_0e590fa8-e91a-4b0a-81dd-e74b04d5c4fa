.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-screen.fade-out {
    opacity: 0;
}

.loading-content {
    text-align: center;
    z-index: 2;
}

.logo-container {
    margin-bottom: 2rem;
}

.animated-logo {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.logo-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid transparent;
    animation: rotate 3s linear infinite;
}

.ring-1 {
    width: 120px;
    height: 120px;
    border-top: 2px solid #4facfe;
    border-right: 2px solid #4facfe;
    animation-duration: 2s;
}

.ring-2 {
    width: 90px;
    height: 90px;
    top: 15px;
    left: 15px;
    border-bottom: 2px solid #667eea;
    border-left: 2px solid #667eea;
    animation-duration: 3s;
    animation-direction: reverse;
}

.ring-3 {
    width: 60px;
    height: 60px;
    top: 30px;
    left: 30px;
    border-top: 2px solid #00f2fe;
    border-right: 2px solid #00f2fe;
    animation-duration: 1.5s;
}

.logo-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4facfe, #667eea);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    color: white;
    animation: pulse 2s ease-in-out infinite;
}

.loading-text h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #4facfe 0%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-text p {
    color: #b8bcc8;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.progress-container {
    width: 300px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #667eea);
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 1.5s infinite;
}

.progress-text {
    color: #4facfe;
    font-weight: 600;
    font-size: 0.9rem;
}

.loading-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #4facfe;
    border-radius: 50%;
    animation: float-particle 8s infinite linear;
}

.particle:nth-child(odd) {
    background: #667eea;
}

.particle:nth-child(3n) {
    background: #00f2fe;
}

/* Particle positions and delays */
.particle-1 { top: 10%; left: 10%; animation-delay: 0s; }
.particle-2 { top: 20%; left: 80%; animation-delay: 1s; }
.particle-3 { top: 80%; left: 20%; animation-delay: 2s; }
.particle-4 { top: 60%; left: 90%; animation-delay: 3s; }
.particle-5 { top: 30%; left: 5%; animation-delay: 4s; }
.particle-6 { top: 90%; left: 70%; animation-delay: 0.5s; }
.particle-7 { top: 15%; left: 60%; animation-delay: 1.5s; }
.particle-8 { top: 70%; left: 15%; animation-delay: 2.5s; }
.particle-9 { top: 40%; left: 85%; animation-delay: 3.5s; }
.particle-10 { top: 85%; left: 40%; animation-delay: 4.5s; }
.particle-11 { top: 25%; left: 30%; animation-delay: 0.8s; }
.particle-12 { top: 75%; left: 60%; animation-delay: 1.8s; }
.particle-13 { top: 50%; left: 10%; animation-delay: 2.8s; }
.particle-14 { top: 10%; left: 50%; animation-delay: 3.8s; }
.particle-15 { top: 95%; left: 25%; animation-delay: 4.8s; }
.particle-16 { top: 35%; left: 75%; animation-delay: 1.2s; }
.particle-17 { top: 65%; left: 35%; animation-delay: 2.2s; }
.particle-18 { top: 5%; left: 85%; animation-delay: 3.2s; }
.particle-19 { top: 80%; left: 5%; animation-delay: 4.2s; }
.particle-20 { top: 45%; left: 55%; animation-delay: 0.3s; }

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes float-particle {
    0% {
        transform: translateY(0px) translateX(0px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) translateX(50px);
        opacity: 0;
    }
}

@media (max-width: 768px) {
    .animated-logo {
        width: 100px;
        height: 100px;
    }
    
    .ring-1 {
        width: 100px;
        height: 100px;
    }
    
    .ring-2 {
        width: 75px;
        height: 75px;
        top: 12.5px;
        left: 12.5px;
    }
    
    .ring-3 {
        width: 50px;
        height: 50px;
        top: 25px;
        left: 25px;
    }
    
    .logo-center {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .loading-text h2 {
        font-size: 1.5rem;
    }
    
    .progress-container {
        width: 250px;
    }
}
