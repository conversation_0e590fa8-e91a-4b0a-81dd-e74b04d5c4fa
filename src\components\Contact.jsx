import { useState } from 'react'
import './Contact.css'

const Contact = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        subject: '',
        message: ''
    })

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        })
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        // Handle form submission here
        console.log('Form submitted:', formData)
        alert('Thank you for your message! I will get back to you soon.')
        setFormData({
            name: '',
            email: '',
            subject: '',
            message: ''
        })
    }

    return (
        <section id="contact" className="contact">
            <div className="container">
                <div className="section-header" data-aos="fade-up">
                    <h2 className="section-title">Get In Touch</h2>
                    <p className="section-subtitle">Let's work together on your next project</p>
                </div>
                <div className="contact-content">
                    <div className="contact-info" data-aos="fade-right" data-aos-delay="200">
                        <h3>Let's Connect</h3>
                        <p>I'm always interested in hearing about new projects and opportunities.</p>
                        <div className="contact-item">
                            <i className="fas fa-envelope"></i>
                            <div>
                                <h4>Email</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div className="contact-item">
                            <i className="fas fa-phone"></i>
                            <div>
                                <h4>Phone</h4>
                                <p>+92 XXX XXXXXXX</p>
                            </div>
                        </div>
                        <div className="contact-item">
                            <i className="fas fa-map-marker-alt"></i>
                            <div>
                                <h4>Location</h4>
                                <p>Pakistan</p>
                            </div>
                        </div>
                        <div className="social-links">
                            <a href="#" className="social-link">
                                <i className="fab fa-linkedin"></i>
                            </a>
                            <a href="#" className="social-link">
                                <i className="fab fa-github"></i>
                            </a>
                            <a href="#" className="social-link">
                                <i className="fab fa-twitter"></i>
                            </a>
                        </div>
                    </div>
                    <form 
                        className="contact-form" 
                        data-aos="fade-up" 
                        data-aos-delay="400"
                        onSubmit={handleSubmit}
                    >
                        <div className="form-group">
                            <input 
                                type="text" 
                                id="name" 
                                name="name" 
                                value={formData.name}
                                onChange={handleChange}
                                required 
                            />
                            <label htmlFor="name">Your Name</label>
                        </div>
                        <div className="form-group">
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                value={formData.email}
                                onChange={handleChange}
                                required 
                            />
                            <label htmlFor="email">Your Email</label>
                        </div>
                        <div className="form-group">
                            <input 
                                type="text" 
                                id="subject" 
                                name="subject" 
                                value={formData.subject}
                                onChange={handleChange}
                                required 
                            />
                            <label htmlFor="subject">Subject</label>
                        </div>
                        <div className="form-group">
                            <textarea 
                                id="message" 
                                name="message" 
                                rows="5" 
                                value={formData.message}
                                onChange={handleChange}
                                required
                            ></textarea>
                            <label htmlFor="message">Your Message</label>
                        </div>
                        <button type="submit" className="btn btn-primary">
                            <i className="fas fa-paper-plane"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </section>
    )
}

export default Contact
