import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Text, Sphere, Box, Torus, Octahedron, Float, MeshDistortMaterial, Environment, Sparkles } from '@react-three/drei'
import * as THREE from 'three'
import './Hero.css'

// Enhanced 3D Tech Node Component
const TechNode = ({ position, color, name, icon, shape = 'sphere' }) => {
    const meshRef = useRef()
    const textRef = useRef()

    useFrame((state) => {
        if (meshRef.current) {
            meshRef.current.rotation.x += 0.008
            meshRef.current.rotation.y += 0.012
            meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + position[0]) * 0.1
        }
    })

    const GeometryComponent = () => {
        switch (shape) {
            case 'box':
                return <Box args={[0.4, 0.4, 0.4]} />
            case 'torus':
                return <Torus args={[0.3, 0.1, 8, 16]} />
            case 'octahedron':
                return <Octahedron args={[0.35]} />
            default:
                return <Sphere args={[0.3, 32, 32]} />
        }
    }

    return (
        <Float speed={2} rotationIntensity={1} floatIntensity={0.5}>
            <group position={position}>
                <mesh ref={meshRef}>
                    <GeometryComponent />
                    <MeshDistortMaterial
                        color={color}
                        attach="material"
                        distort={0.3}
                        speed={2}
                        roughness={0.1}
                        metalness={0.8}
                    />
                </mesh>
                <Text
                    ref={textRef}
                    position={[0, -0.7, 0]}
                    fontSize={0.18}
                    color="white"
                    anchorX="center"
                    anchorY="middle"
                    font="/fonts/Inter-Bold.woff"
                >
                    {name}
                </Text>
                {/* Glowing ring around each node */}
                <Torus args={[0.5, 0.02, 8, 32]} position={[0, 0, 0]}>
                    <meshBasicMaterial color={color} transparent opacity={0.3} />
                </Torus>
            </group>
        </Float>
    )
}

// Central Core Component
const CentralCore = () => {
    const coreRef = useRef()
    const innerCoreRef = useRef()

    useFrame((state) => {
        if (coreRef.current) {
            coreRef.current.rotation.x += 0.005
            coreRef.current.rotation.y += 0.01
        }
        if (innerCoreRef.current) {
            innerCoreRef.current.rotation.x -= 0.008
            innerCoreRef.current.rotation.z += 0.006
        }
    })

    return (
        <group>
            {/* Outer core */}
            <mesh ref={coreRef}>
                <Octahedron args={[0.8]} />
                <MeshDistortMaterial
                    color="#4facfe"
                    attach="material"
                    distort={0.4}
                    speed={3}
                    roughness={0.1}
                    metalness={0.9}
                    transparent
                    opacity={0.8}
                />
            </mesh>
            {/* Inner core */}
            <mesh ref={innerCoreRef}>
                <Sphere args={[0.5, 32, 32]} />
                <MeshDistortMaterial
                    color="#667eea"
                    attach="material"
                    distort={0.2}
                    speed={4}
                    roughness={0.0}
                    metalness={1.0}
                    transparent
                    opacity={0.6}
                />
            </mesh>
        </group>
    )
}

const Professional3DDiagram = () => {
    const groupRef = useRef()
    const particlesRef = useRef()

    const techData = [
        { name: 'React', color: '#61dafb', icon: '⚛️', shape: 'octahedron' },
        { name: 'Node.js', color: '#339933', icon: '🚀', shape: 'box' },
        { name: 'MongoDB', color: '#47a248', icon: '🍃', shape: 'sphere' },
        { name: 'Express', color: '#ffffff', icon: '🚀', shape: 'torus' },
        { name: 'JavaScript', color: '#f7df1e', icon: '⚡', shape: 'octahedron' },
        { name: 'TypeScript', color: '#3178c6', icon: '📘', shape: 'box' },
        { name: 'Next.js', color: '#000000', icon: '▲', shape: 'sphere' },
        { name: 'AWS', color: '#ff9900', icon: '☁️', shape: 'torus' }
    ]

    useFrame((state) => {
        if (groupRef.current) {
            groupRef.current.rotation.y += 0.003
        }
    })

    return (
        <group ref={groupRef}>
            {/* Central core */}
            <CentralCore />

            {/* Tech nodes in multiple orbits */}
            {techData.map((tech, index) => {
                const orbit = index < 4 ? 0 : 1
                const itemsInOrbit = orbit === 0 ? 4 : 4
                const orbitIndex = orbit === 0 ? index : index - 4
                const angle = (orbitIndex / itemsInOrbit) * Math.PI * 2
                const radius = orbit === 0 ? 2.5 : 4
                const x = Math.cos(angle) * radius
                const z = Math.sin(angle) * radius
                const y = orbit === 0 ? Math.sin(angle * 3) * 0.3 : Math.sin(angle * 2) * 0.8

                return (
                    <TechNode
                        key={tech.name}
                        position={[x, y, z]}
                        color={tech.color}
                        name={tech.name}
                        icon={tech.icon}
                        shape={tech.shape}
                    />
                )
            })}

            {/* Connecting lines/particles */}
            <Sparkles count={100} scale={8} size={2} speed={0.4} color="#4facfe" />
        </group>
    )
}

const Hero = () => {
    const handleNavClick = (e, targetId) => {
        e.preventDefault()
        const targetElement = document.getElementById(targetId)
        if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' })
        }
    }

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.3,
                delayChildren: 0.2
            }
        }
    }

    const itemVariants = {
        hidden: { opacity: 0, y: 30 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                ease: "easeOut"
            }
        }
    }

    const titleVariants = {
        hidden: { opacity: 0, y: 50 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 1,
                ease: "easeOut"
            }
        }
    }

    const buttonVariants = {
        hidden: { opacity: 0, scale: 0.8 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        },
        hover: {
            scale: 1.05,
            transition: {
                duration: 0.2,
                ease: "easeInOut"
            }
        }
    }

    return (
        <section id="home" className="hero">
            <motion.div
                className="hero-container"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <div className="hero-left">
                    <motion.div className="hero-content" variants={itemVariants}>
                        <motion.h1 className="hero-title" variants={titleVariants}>
                            Hi, I'm <span className="highlight">Qaswar Hussain</span>
                        </motion.h1>
                        <motion.h2 className="hero-subtitle" variants={itemVariants}>
                            MERN Stack Developer
                        </motion.h2>
                        <motion.p className="hero-description" variants={itemVariants}>
                            Passionate about creating innovative web solutions with modern technologies.
                            I build responsive, scalable applications that deliver exceptional user experiences.
                        </motion.p>
                        <motion.div className="hero-buttons" variants={itemVariants}>
                            <motion.a
                                href="#projects"
                                className="btn btn-primary btn-lg"
                                onClick={(e) => handleNavClick(e, 'projects')}
                                variants={buttonVariants}
                                whileHover="hover"
                                whileTap={{ scale: 0.95 }}
                            >
                                <span>View My Work</span>
                                <i className="fas fa-arrow-right"></i>
                            </motion.a>
                            <motion.a
                                href="#contact"
                                className="btn btn-secondary btn-lg"
                                onClick={(e) => handleNavClick(e, 'contact')}
                                variants={buttonVariants}
                                whileHover="hover"
                                whileTap={{ scale: 0.95 }}
                            >
                                <span>Get In Touch</span>
                                <i className="fas fa-paper-plane"></i>
                            </motion.a>
                        </motion.div>
                        <motion.div
                            className="hero-scroll"
                            variants={itemVariants}
                            whileHover={{ y: -5 }}
                            transition={{ duration: 0.2 }}
                        >
                            <div className="scroll-indicator">
                                <span>Scroll Down</span>
                                <motion.i
                                    className="fas fa-arrow-down"
                                    animate={{ y: [0, 5, 0] }}
                                    transition={{
                                        duration: 2,
                                        repeat: Infinity,
                                        ease: "easeInOut"
                                    }}
                                ></motion.i>
                            </div>
                        </motion.div>
                    </div>
                    </motion.div>
                </div>
                <motion.div
                    className="hero-right"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
                >
                    <motion.div
                        className="professional-3d-container"
                        whileHover={{
                            scale: 1.02,
                            rotateY: 5,
                            transition: { duration: 0.3 }
                        }}
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 1.2, delay: 0.8, ease: "easeOut" }}
                    >
                        <Canvas
                            camera={{ position: [0, 3, 12], fov: 60 }}
                            gl={{ antialias: true, alpha: true }}
                            dpr={[1, 2]}
                        >
                            {/* Enhanced lighting setup */}
                            <ambientLight intensity={0.3} />
                            <directionalLight
                                position={[10, 10, 5]}
                                intensity={1}
                                castShadow
                                shadow-mapSize-width={2048}
                                shadow-mapSize-height={2048}
                            />
                            <pointLight position={[-10, -10, -10]} intensity={0.5} color="#4facfe" />
                            <pointLight position={[10, 10, 10]} intensity={0.5} color="#667eea" />
                            <spotLight
                                position={[0, 20, 0]}
                                intensity={0.8}
                                angle={0.3}
                                penumbra={1}
                                color="#ffffff"
                            />

                            {/* Environment for reflections */}
                            <Environment preset="city" />

                            {/* Main 3D diagram */}
                            <Professional3DDiagram />

                            {/* Enhanced controls */}
                            <OrbitControls
                                enableZoom={false}
                                enablePan={false}
                                autoRotate={true}
                                autoRotateSpeed={0.3}
                                maxPolarAngle={Math.PI / 2}
                                minPolarAngle={Math.PI / 3}
                            />
                        </Canvas>
                    </motion.div>
                </motion.div>
            </motion.div>
            <div className="hero-bg">
                <div className="floating-shapes">
                    <div className="shape shape-1"></div>
                    <div className="shape shape-2"></div>
                    <div className="shape shape-3"></div>
                    <div className="shape shape-4"></div>
                    <div className="shape shape-5"></div>
                    <div className="shape shape-6"></div>
                </div>
            </div>
        </section>
    )
}

export default Hero
