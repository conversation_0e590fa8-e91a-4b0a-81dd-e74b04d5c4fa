import { useEffect, useState } from 'react'

const CustomCursor = () => {
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
    const [cursorVariant, setCursorVariant] = useState('default')

    useEffect(() => {
        const mouseMove = (e) => {
            setMousePosition({
                x: e.clientX,
                y: e.clientY
            })
        }

        const mouseEnterLink = () => setCursorVariant('link')
        const mouseLeaveLink = () => setCursorVariant('default')

        window.addEventListener('mousemove', mouseMove)

        // Add event listeners to interactive elements
        const links = document.querySelectorAll('a, button, .btn')
        links.forEach(link => {
            link.addEventListener('mouseenter', mouseEnterLink)
            link.addEventListener('mouseleave', mouseLeaveLink)
        })

        return () => {
            window.removeEventListener('mousemove', mouseMove)
            links.forEach(link => {
                link.removeEventListener('mouseenter', mouseEnterLink)
                link.removeEventListener('mouseleave', mouseLeaveLink)
            })
        }
    }, [])

    const variants = {
        default: {
            x: mousePosition.x - 10,
            y: mousePosition.y - 10,
            scale: 1
        },
        link: {
            x: mousePosition.x - 20,
            y: mousePosition.y - 20,
            scale: 2
        }
    }

    return (
        <>
            <div 
                className="custom-cursor"
                style={{
                    left: `${variants[cursorVariant].x}px`,
                    top: `${variants[cursorVariant].y}px`,
                    transform: `scale(${variants[cursorVariant].scale})`
                }}
            />
            <div 
                className="custom-cursor-follower"
                style={{
                    left: `${mousePosition.x - 20}px`,
                    top: `${mousePosition.y - 20}px`
                }}
            />
        </>
    )
}

export default CustomCursor
