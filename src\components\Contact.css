/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #e4e6ea;
}

.contact-info > p {
    color: #8892b0;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.contact-item div h4 {
    color: #e4e6ea;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.contact-item div p {
    color: #8892b0;
    margin: 0;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8892b0;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.contact-form {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: #e4e6ea;
    font-size: 1rem;
    transition: all 0.3s ease;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group label {
    position: absolute;
    top: 15px;
    left: 15px;
    color: #8892b0;
    font-size: 1rem;
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-group input:focus + label,
.form-group textarea:focus + label,
.form-group input:valid + label,
.form-group textarea:valid + label {
    top: -10px;
    left: 10px;
    font-size: 0.8rem;
    color: #667eea;
    background: #0a0a0a;
    padding: 0 5px;
}

.form-group button {
    width: 100%;
    margin-top: 1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-info h3 {
        font-size: 1.8rem;
    }
    
    .contact-info > p {
        font-size: 1rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .contact {
        padding: 80px 0;
    }
    
    .contact-info h3 {
        font-size: 1.5rem;
    }
    
    .contact-item {
        gap: 0.8rem;
    }
    
    .contact-item i {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .contact-form {
        padding: 1rem;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 12px;
        font-size: 0.9rem;
    }
    
    .form-group label {
        font-size: 0.9rem;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
