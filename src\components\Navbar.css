/* Enhanced Navigation with Glassmorphism */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    z-index: 1000;
    transition: all var(--transition-normal);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-2) 0;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.95);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-bottom-color: rgba(255, 255, 255, 0.2);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    position: relative;
}

/* Enhanced Logo */
.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    position: relative;
    cursor: pointer;
}

.logo-text {
    font-family: var(--font-heading);
    font-size: var(--text-3xl);
    font-weight: 800;
    background: var(--text-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    position: relative;
}

.logo-dot {
    width: 8px;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 50%;
    box-shadow: var(--shadow-glow);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

/* Enhanced Navigation Menu */
.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-8);
    align-items: center;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    padding: var(--space-2) var(--space-6);
    box-shadow: var(--shadow-lg);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--neutral-200);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--text-sm);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link i {
    font-size: var(--text-sm);
    opacity: 0.7;
    transition: all var(--transition-normal);
}

.nav-link:hover {
    color: var(--neutral-100);
    background: var(--glass-bg);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-link:hover i {
    opacity: 1;
    color: var(--primary-blue);
}

.nav-link.active {
    color: var(--primary-blue);
    background: rgba(79, 172, 254, 0.1);
    box-shadow: var(--shadow-md);
}

.nav-link.active i {
    color: var(--primary-blue);
    opacity: 1;
}

.nav-indicator {
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-glow);
}

/* Enhanced Mobile Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.nav-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.bar {
    width: 20px;
    height: 2px;
    background: var(--neutral-200);
    margin: 2px 0;
    transition: all var(--transition-normal);
    border-radius: var(--radius-full);
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-4px, 4px);
    background: var(--primary-blue);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
    transform: translateX(20px);
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-4px, -4px);
    background: var(--primary-blue);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 999;
}

/* Enhanced Mobile Navigation */
@media (max-width: 768px) {
    .nav-container {
        height: 70px;
        padding: 0 var(--space-4);
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        width: 100%;
        height: calc(100vh - 70px);
        text-align: center;
        transition: all var(--transition-normal);
        box-shadow: var(--shadow-2xl);
        border: none;
        border-radius: 0;
        padding: var(--space-8) var(--space-4);
        gap: var(--space-4);
        justify-content: flex-start;
        align-items: stretch;
        z-index: 1000;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        width: 100%;
    }

    .nav-link {
        width: 100%;
        justify-content: center;
        padding: var(--space-4) var(--space-6);
        font-size: var(--text-lg);
        border-radius: var(--radius-xl);
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        margin-bottom: var(--space-2);
    }

    .nav-link:hover,
    .nav-link.active {
        background: rgba(79, 172, 254, 0.1);
        border-color: var(--primary-blue);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg), var(--shadow-glow);
    }

    .nav-link i {
        font-size: var(--text-lg);
    }

    .nav-toggle {
        display: flex;
    }

    .logo-text {
        font-size: var(--text-2xl);
    }

    .logo-dot {
        width: 6px;
        height: 6px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--space-3);
    }

    .logo-text {
        font-size: var(--text-xl);
    }

    .nav-menu {
        padding: var(--space-6) var(--space-3);
    }

    .nav-link {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-base);
    }
}
