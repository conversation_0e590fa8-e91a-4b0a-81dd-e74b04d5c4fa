<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> - MERN Stack Developer</title>
    <meta name="description" content="Passionate MERN Stack Developer creating innovative web solutions with modern technologies. Building responsive, scalable applications." />
    <meta name="keywords" content="MERN Stack Developer, Web Developer, React, Node.js, MongoDB, Express.js, Portfolio" />
    <meta name="author" content="<PERSON><PERSON><PERSON>" />

    <!-- Enhanced Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Qaswar Hussain - MERN Stack Developer">
    <meta property="og:description" content="Passionate MERN Stack Developer creating innovative web solutions with modern technologies.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://qaswar-portfolio.com">
    <meta property="og:image" content="/og-image.jpg">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Qaswar Hussain - MERN Stack Developer">
    <meta name="twitter:description" content="Passionate MERN Stack Developer creating innovative web solutions with modern technologies.">
    <meta name="twitter:image" content="/og-image.jpg">

    <!-- Theme Color -->
    <meta name="theme-color" content="#4facfe">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
