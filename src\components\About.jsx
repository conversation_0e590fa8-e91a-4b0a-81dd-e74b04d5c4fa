import { motion } from 'framer-motion'
import './About.css'
import profileImg from '../assets/Profile1.jpeg'
import resumePdf from '../assets/qaswar <PERSON>-Resume.pdf'

const About = () => {
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.3,
                delayChildren: 0.2
            }
        }
    }

    const itemVariants = {
        hidden: { opacity: 0, y: 30 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                ease: "easeOut"
            }
        }
    }

    const imageVariants = {
        hidden: { opacity: 0, x: -50, scale: 0.8 },
        visible: {
            opacity: 1,
            x: 0,
            scale: 1,
            transition: {
                duration: 1,
                ease: "easeOut"
            }
        }
    }

    const textVariants = {
        hidden: { opacity: 0, x: 50 },
        visible: {
            opacity: 1,
            x: 0,
            transition: {
                duration: 0.8,
                ease: "easeOut"
            }
        }
    }

    const statVariants = {
        hidden: { opacity: 0, scale: 0.5 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        }
    }

    return (
        <section id="about" className="about">
            <div className="container">
                <motion.div
                    className="section-header"
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={containerVariants}
                >
                    <motion.h2 className="section-title" variants={itemVariants}>
                        About Me
                    </motion.h2>
                    <motion.p className="section-subtitle" variants={itemVariants}>
                        Get to know more about my background and expertise
                    </motion.p>
                </motion.div>
                <motion.div
                    className="about-content"
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={containerVariants}
                >
                    <motion.div className="about-image" variants={imageVariants}>
                        <motion.img
                            src={profileImg}
                            alt="Qaswar Hussain"
                            className="profile-img"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.3 }}
                        />
                        <div className="image-border"></div>
                    </motion.div>
                    <motion.div className="about-text" variants={textVariants}>
                        <motion.h3 variants={itemVariants}>
                            Hello! I'm Qaswar Hussain
                        </motion.h3>
                        <motion.p variants={itemVariants}>
                            I'm a passionate MERN Stack Developer with expertise in building modern,
                            responsive web applications. With a strong foundation in MongoDB, Express.js,
                            React, and Node.js, I create full-stack solutions that are both functional
                            and visually appealing.
                        </motion.p>
                        <motion.p variants={itemVariants}>
                            My journey in web development has led me to work on various projects,
                            from simple calculators to complex educational platforms. I'm always
                            eager to learn new technologies and take on challenging projects that
                            push the boundaries of what's possible on the web.
                        </motion.p>
                        <motion.div
                            className="about-stats"
                            variants={containerVariants}
                        >
                            <motion.div className="stat" variants={statVariants}>
                                <motion.h4
                                    initial={{ scale: 0 }}
                                    whileInView={{ scale: 1 }}
                                    transition={{ duration: 0.5, delay: 0.2 }}
                                    viewport={{ once: true }}
                                >
                                    15+
                                </motion.h4>
                                <p>Projects Completed</p>
                            </motion.div>
                            <motion.div className="stat" variants={statVariants}>
                                <motion.h4
                                    initial={{ scale: 0 }}
                                    whileInView={{ scale: 1 }}
                                    transition={{ duration: 0.5, delay: 0.4 }}
                                    viewport={{ once: true }}
                                >
                                    2+
                                </motion.h4>
                                <p>Years Experience</p>
                            </motion.div>
                            <motion.div className="stat" variants={statVariants}>
                                <motion.h4
                                    initial={{ scale: 0 }}
                                    whileInView={{ scale: 1 }}
                                    transition={{ duration: 0.5, delay: 0.6 }}
                                    viewport={{ once: true }}
                                >
                                    100%
                                </motion.h4>
                                <p>Client Satisfaction</p>
                            </motion.div>
                        </motion.div>
                        <motion.a
                            href={resumePdf}
                            download
                            className="btn btn-primary"
                            variants={itemVariants}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <i className="fas fa-download"></i>
                            <span>Download Resume</span>
                        </motion.a>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    )
}

export default About
