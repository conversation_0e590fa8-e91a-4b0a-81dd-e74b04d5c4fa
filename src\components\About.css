/* Enhanced About Section */
.about {
    padding: var(--space-32) 0;
    background: var(--neutral-900);
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 80% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.about .container {
    position: relative;
    z-index: 2;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--space-20);
    align-items: center;
    margin-top: var(--space-16);
}

.about-image {
    position: relative;
    display: flex;
    justify-content: center;
}

.profile-img {
    width: 350px;
    height: 350px;
    border-radius: 50%;
    object-fit: cover;
    border: 6px solid transparent;
    background: var(--bg-secondary);
    padding: 6px;
    z-index: 3;
    position: relative;
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    transition: all var(--transition-normal);
}

.profile-img:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-2xl), var(--shadow-glow-lg);
}

.image-border {
    position: absolute;
    top: -15px;
    left: -15px;
    width: 380px;
    height: 380px;
    border-radius: 50%;
    background: var(--bg-secondary);
    opacity: 0.2;
    z-index: 1;
    animation: pulse 3s ease-in-out infinite;
}

.image-border::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    height: 90%;
    border-radius: 50%;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.2;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.2;
    }
}

/* Enhanced About Text */
.about-text {
    position: relative;
}

.about-text h3 {
    font-family: var(--font-heading);
    font-size: var(--text-4xl);
    font-weight: 700;
    margin-bottom: var(--space-6);
    color: var(--neutral-100);
    background: var(--text-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.about-text p {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--neutral-300);
    margin-bottom: var(--space-6);
    font-weight: 400;
}

/* Enhanced Stats */
.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
    margin: var(--space-10) 0;
    padding: var(--space-8);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

.stat {
    text-align: center;
    position: relative;
    padding: var(--space-4);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
}

.stat:hover {
    background: var(--glass-bg);
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.stat h4 {
    font-size: var(--text-4xl);
    font-weight: 800;
    background: var(--text-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-2);
    line-height: var(--leading-none);
}

.stat p {
    color: var(--neutral-400);
    font-size: var(--text-sm);
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced Button */
.about-text .btn {
    margin-top: var(--space-8);
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .profile-img {
        width: 250px;
        height: 250px;
    }
    
    .image-border {
        width: 270px;
        height: 270px;
    }
    
    .about-text h3 {
        font-size: 1.8rem;
    }
    
    .about-text p {
        font-size: 1rem;
    }
    
    .about-stats {
        justify-content: center;
        gap: 1.5rem;
    }
    
    .stat h4 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .about {
        padding: 80px 0;
    }
    
    .profile-img {
        width: 200px;
        height: 200px;
    }
    
    .image-border {
        width: 220px;
        height: 220px;
    }
    
    .about-text h3 {
        font-size: 1.5rem;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat h4 {
        font-size: 1.2rem;
    }
}
