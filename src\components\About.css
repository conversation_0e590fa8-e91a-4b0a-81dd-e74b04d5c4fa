/* About Section */
.about {
    padding: 100px 0;
    background: #0a0a0a;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.about-image {
    position: relative;
    display: flex;
    justify-content: center;
}

.profile-img {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid transparent;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4px;
    z-index: 2;
    position: relative;
}

.image-border {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 320px;
    height: 320px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.3;
    z-index: 1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.3;
    }
}

.about-text h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #e4e6ea;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #8892b0;
    margin-bottom: 1.5rem;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
}

.stat {
    text-align: center;
}

.stat h4 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat p {
    color: #8892b0;
    font-size: 0.9rem;
    margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .profile-img {
        width: 250px;
        height: 250px;
    }
    
    .image-border {
        width: 270px;
        height: 270px;
    }
    
    .about-text h3 {
        font-size: 1.8rem;
    }
    
    .about-text p {
        font-size: 1rem;
    }
    
    .about-stats {
        justify-content: center;
        gap: 1.5rem;
    }
    
    .stat h4 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .about {
        padding: 80px 0;
    }
    
    .profile-img {
        width: 200px;
        height: 200px;
    }
    
    .image-border {
        width: 220px;
        height: 220px;
    }
    
    .about-text h3 {
        font-size: 1.5rem;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat h4 {
        font-size: 1.2rem;
    }
}
